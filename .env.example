# PrintMind 环境变量配置示例
# 复制此文件为 .env 并填入实际值



# 应用配置
DEBUG=true
APP_NAME=PrintMind
VERSION=1.0.0

# 服务器配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
FRONTEND_PORT=80

# 文件上传配置
MAX_FILE_SIZE=52428800  # 50MB in bytes
UPLOAD_DIR=uploads

# PDF生成配置
PDF_DPI=300
PDF_FORMAT=A4

# 字体配置
FONT_DIR=fonts
DEFAULT_FONT=NotoSansCJK-Regular.ttc

# Doubao AI配置
DOUBAO_API_KEY=2ad1b7d4-5323-4668-b529-2fe275295a7b
DOUBAO_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
DOUBAO_MODEL=doubao-seed-1-6-250615
DOUBAO_MAX_TOKENS=2000
DOUBAO_TEMPERATURE=0.7

# 数据库配置（如果需要）
# DATABASE_URL=sqlite:///./printmind.db

# Redis配置（如果需要缓存）
# REDIS_URL=redis://localhost:6379

# 日志配置
LOG_LEVEL=INFO

# 在线部署配置
# 前端环境变量（Vercel/Netlify）
VITE_API_BASE_URL=http://localhost:8000

# 后端环境变量（Railway/Render）
PORT=8000
PYTHONUNBUFFERED=1

# CORS配置
CORS_ORIGINS=http://localhost:5173,http://localhost:3000,http://localhost:5176

# 生产环境示例
# VITE_API_BASE_URL=https://your-backend.railway.app
# CORS_ORIGINS=https://your-frontend.vercel.app
# DEBUG=false
