# PrintMind LaTeX数学公式完整指南

## 🎯 功能概述

PrintMind现已完全支持LaTeX数学公式，可以在Markdown文档中渲染美观的数学表达式。

### ✅ 支持的功能
- **行内公式**：`$...$` 格式
- **块级公式**：`$$...$$` 格式
- **分数**：`\frac{分子}{分母}`
- **指数和下标**：`x^2`, `x_1`
- **根号**：`\sqrt{x}`, `\sqrt[n]{x}`
- **希腊字母**：`\alpha`, `\beta`, `\pi` 等
- **数学符号**：`\times`, `\div`, `\pm` 等
- **求和积分**：`\sum`, `\int`, `\lim`
- **三角函数**：`\sin`, `\cos`, `\tan`

## 📝 基础语法

### 1. 行内公式
```markdown
这是一个行内公式：$\frac{1}{2} + \frac{1}{3} = \frac{5}{6}$
```
显示效果：这是一个行内公式：$\frac{1}{2} + \frac{1}{3} = \frac{5}{6}$

### 2. 块级公式
```markdown
$$\frac{a}{b} = \frac{c}{d}$$
```
显示效果：
$$\frac{a}{b} = \frac{c}{d}$$

## 🔢 分数表示

### 基础分数
```latex
$\frac{1}{2}$          # 二分之一
$\frac{3}{4}$          # 四分之三
$\frac{a}{b}$          # 通用分数
```

### 复杂分数
```latex
$\frac{x^2 + 1}{x - 1}$              # 分子分母都有表达式
$\frac{\frac{1}{2}}{\frac{3}{4}}$    # 分数的分数
```

### 带分数
```latex
$2\frac{1}{3}$         # 二又三分之一
$1\frac{3}{4}$         # 一又四分之三
```

## 🧮 算数题专用格式

PrintMind的算数题功能现在支持LaTeX格式：

### 整数运算
```latex
**第1题：** $6 \times 7 = 42$
**第2题：** $15 + 28 = 43$
**第3题：** $84 \div 12 = 7$
**第4题：** $50 - 23 = 27$
```

### 分数运算
```latex
**第1题：** $\frac{1}{2} + \frac{1}{4} = \frac{3}{4}$
**第2题：** $\frac{2}{3} \times \frac{3}{5} = \frac{2}{5}$
**第3题：** $\frac{3}{4} \div \frac{1}{2} = \frac{3}{2}$
**第4题：** $\frac{5}{6} - \frac{1}{3} = \frac{1}{2}$
```

### 带分数运算
```latex
**第1题：** $2\frac{1}{2} + 1\frac{1}{4} = 3\frac{3}{4}$
**第2题：** $3\frac{2}{3} - 1\frac{1}{6} = 2\frac{1}{2}$
```

## 🔧 数学工具栏使用

PrintMind提供了可视化的数学工具栏：

### 1. 打开工具栏
- 在编辑器状态栏点击"∑ 公式"按钮
- 工具栏会在编辑器右上角显示

### 2. 插入公式
- **分数**：点击分数按钮插入 `$\frac{a}{b}$`
- **指数**：点击指数按钮插入 `$x^2$`
- **根号**：点击根号按钮插入 `$\sqrt{x}$`
- **希腊字母**：点击对应按钮插入各种希腊字母
- **运算符**：点击运算符按钮插入数学符号

### 3. 快速插入
- **行内公式**：插入 `$公式$` 模板
- **块级公式**：插入 `$$公式$$` 模板

## 📚 常用公式示例

### 分数运算
```latex
# 分数加法
$\frac{1}{3} + \frac{1}{6} = \frac{2 + 1}{6} = \frac{1}{2}$

# 分数乘法
$\frac{2}{3} \times \frac{3}{4} = \frac{6}{12} = \frac{1}{2}$

# 分数除法
$\frac{3}{4} \div \frac{1}{2} = \frac{3}{4} \times \frac{2}{1} = \frac{3}{2}$
```

### 指数和根号
```latex
$x^2 + y^2 = z^2$                    # 勾股定理
$\sqrt{16} = 4$                      # 平方根
$\sqrt[3]{27} = 3$                   # 立方根
$x^{2n+1}$                           # 复杂指数
```

### 求和与积分
```latex
$\sum_{i=1}^{n} i = \frac{n(n+1)}{2}$        # 求和公式
$\int_{0}^{1} x^2 dx = \frac{1}{3}$          # 定积分
$\lim_{x \to 0} \frac{\sin x}{x} = 1$        # 极限
```

### 三角函数
```latex
$\sin^2 x + \cos^2 x = 1$           # 三角恒等式
$\tan x = \frac{\sin x}{\cos x}$    # 正切定义
```

## 🎨 样式和显示

### 字体大小
- 行内公式自动适应文本大小
- 块级公式使用标准数学字体大小
- 可通过CSS调整整体缩放

### 对齐方式
- 行内公式：与文本基线对齐
- 块级公式：居中显示
- 多行公式：支持对齐环境

### 颜色和样式
- 默认使用黑色数学字体
- 错误公式显示红色边框
- 支持自定义CSS样式

## 🚀 最佳实践

### 1. 选择合适的格式
- **简单计算**：使用行内公式 `$2 + 3 = 5$`
- **重要公式**：使用块级公式突出显示
- **算数题**：统一使用LaTeX格式保持美观

### 2. 分数书写规范
- **简单分数**：`$\frac{1}{2}$`
- **复杂分数**：确保分子分母用花括号包围
- **带分数**：`$2\frac{1}{3}$` 而不是 `$2 \frac{1}{3}$`

### 3. 运算符使用
- **乘法**：使用 `\times` 而不是 `*`
- **除法**：使用 `\div` 而不是 `/`
- **等号**：直接使用 `=`

### 4. 错误处理
- 语法错误的公式会显示原始文本
- 检查花括号是否匹配
- 确保反斜杠正确转义

## 🔍 故障排除

### 常见问题

**Q: 公式不显示，只显示原始代码？**
A: 检查是否正确使用了 `$...$` 或 `$$...$$` 包围公式

**Q: 分数显示不正确？**
A: 确保使用 `\frac{分子}{分母}` 格式，分子分母用花括号包围

**Q: 希腊字母不显示？**
A: 确保使用正确的LaTeX命令，如 `\alpha`, `\beta`, `\pi`

**Q: 公式显示为红色？**
A: 表示语法错误，检查LaTeX语法是否正确

### 调试技巧
1. 先在测试页面验证公式语法
2. 使用数学工具栏生成标准格式
3. 参考本指南的示例代码
4. 检查浏览器控制台的错误信息

## 📈 高级功能

### 1. 矩阵
```latex
$$\begin{pmatrix}
a & b \\
c & d
\end{pmatrix}$$
```

### 2. 多行公式
```latex
$$\begin{align}
x + y &= 5 \\
2x - y &= 1
\end{align}$$
```

### 3. 分段函数
```latex
$$f(x) = \begin{cases}
x^2 & \text{if } x \geq 0 \\
-x^2 & \text{if } x < 0
\end{cases}$$
```

---

通过PrintMind的LaTeX数学公式支持，您可以创建专业、美观的数学文档，特别适合教育、科研和技术文档的编写。
