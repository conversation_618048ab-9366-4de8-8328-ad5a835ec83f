# PrintMind Markdown 语法规范

## 📋 概述

PrintMind 支持标准 Markdown 语法，并扩展了一些特殊语法用于教育文档和排版需求。

## 🔤 标准 Markdown 语法

### 标题
```markdown
# 一级标题
## 二级标题
### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题
```

### 文本格式
```markdown
**粗体文本**
*斜体文本*
`行内代码`
[链接文本](URL)
```

### 列表
```markdown
# 无序列表
- 项目1
- 项目2
  - 嵌套项目

# 有序列表
1. 第一项
2. 第二项
   1. 子项目
```

### 代码块
```markdown
```python
def hello():
    print("Hello World")
```
```

### 表格
```markdown
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
```

## 🎯 PrintMind 扩展语法

### 1. 双括号文本 - 特殊标记
**语法**: `（（内容））`
**效果**: 橙色楷体文字，显示时保留一个括号
**用途**: 重要提示、注释、特殊说明

```markdown
这是普通文本，（（这是特殊标记的内容））。
```

**渲染效果**: 橙色楷体显示为 "（这是特殊标记的内容）"

### 2. 答案框 - 斜杠语法
**语法**: `/内容/` 或多行格式
**效果**: 黄色背景答案框，橙色边框
**用途**: 答案、解析、重要内容框

#### 单行格式
```markdown
/这是答案内容/
```

#### 多行格式
```markdown
/
这是多行答案内容
可以包含多个段落

支持**粗体**和*斜体*格式
/
```

**特殊功能**:
- 支持"答案"和"解析"关键词的图片替换
- 支持多段落内容
- 可控制显示/隐藏（教师版/学生版）

### 3. 编号列表 - 带背景样式
**语法**: `数字. 内容`
**效果**: 使用特殊背景图片的编号列表
**用途**: 步骤说明、要点列举

```markdown
1. 第一个要点
2. 第二个要点
3. 第三个要点
```

### 4. 几何图形
**语法**: 直接使用Unicode字符
**支持的图形**:
- `□` - 空心正方形（50px，黑色）
- `○` - 空心圆形（50px，黑色）

```markdown
选择题：□ A选项  ○ B选项  □ C选项  ○ D选项
```

### 5. 图片扩展语法
**基础语法**: `![描述](URL)`
**扩展语法**: `![描述](URL?参数)`

#### 支持的参数
- `size=small|medium|large|original` - 预设尺寸
- `width=数值` - 自定义宽度
- `height=数值` - 自定义高度  
- `align=left|center|right` - 对齐方式

```markdown
![图片描述](image.jpg?size=medium&align=center)
![自定义尺寸](image.jpg?width=300&height=200&align=left)
```

#### 图片描述格式
- 图片描述应在图片上方
- 居中对齐，单行显示
- 支持并排显示（两张图片可在一行）

### 6. 字体标签支持
**语法**: `<span style="font-family: 字体名">内容</span>`
**用途**: 指定特定字体显示

```markdown
<span style="font-family: 楷体">这段文字使用楷体显示</span>
```

**支持的字体**:
- 楷体 (KaiTi)
- 阿里巴巴普惠体 (Alibaba PuHuiTi)
- 宋体 (SimSun)
- Arial
- Times New Roman

## 📐 排版规范

### 标题规范
1. **一级标题**: 使用图片背景，固定尺寸，居中对齐
2. **标题层级**: 不应跳级（如从#直接到###）
3. **标题间距**: 标题前后应有适当空行

### 段落规范
1. **首行缩进**: 默认启用首行缩进
2. **段落间距**: 段落之间有适当间距
3. **行高**: 默认1.5倍行高

### 列表规范
1. **编号列表**: 使用背景图片样式
2. **无序列表**: 标准项目符号
3. **嵌套**: 支持列表嵌套，正确缩进

### 图片规范
1. **描述位置**: 图片描述在图片上方
2. **对齐方式**: 描述居中，图片可选对齐
3. **并排显示**: 两张图片可在一行显示
4. **间距**: 图片间有适当间距（默认20px）

## ⚠️ 语法注意事项

### 双括号文本
- 必须使用中文括号 `（）`
- 内容不能为空
- 支持嵌套其他格式（粗体、斜体等）

### 答案框
- 斜杠必须在行首和行尾
- 多行格式的开始和结束斜杠必须独占一行
- 支持内部Markdown格式
- 可包含图片和多段落

### 编号列表
- 数字后必须有点号和空格
- 支持多级嵌套
- 自动使用背景图片样式

### 几何图形
- 使用标准Unicode字符
- 固定大小和颜色
- 适用于选择题、填空题等

## 🔍 常见错误

### 标题错误
```markdown
# 错误：标题后没有空格
#正确：标题前有空格

####错误：跳级标题
### 正确：按级别递进
```

### 双括号错误
```markdown
((错误：使用英文括号))
（（正确：使用中文括号））

（（错误：空内容））
（（正确：有实际内容））
```

### 答案框错误
```markdown
/ 错误：斜杠后有空格 /
/正确：斜杠紧贴内容/

/错误：多行格式不规范
内容/

/
正确：多行格式
内容在中间
/
```

### 列表错误
```markdown
1.错误：缺少空格
1. 正确：有空格

1. 第一项
3. 错误：跳号
2. 正确：连续编号
```

## 📊 最佳实践

### 文档结构
1. 使用清晰的标题层级
2. 合理使用特殊语法
3. 保持一致的格式风格

### 教育文档
1. 使用答案框包含答案和解析
2. 利用双括号标记重要概念
3. 使用几何图形制作选择题

### 排版美观
1. 注意空行的使用
2. 图片描述简洁明了
3. 保持段落长度适中

## 🛠️ 工具支持

### 编辑器功能
- 语法高亮
- 实时预览
- 格式化工具栏
- 图片控制面板

### 校验功能
- 语法错误检测
- 格式规范检查
- 结构优化建议
- 错别字检查

---

## 📝 总结

PrintMind Markdown规范在标准Markdown基础上，增加了教育和排版专用的扩展语法，特别适合制作教学文档、试卷、学习资料等。正确使用这些语法可以创建专业、美观的文档。
